<!--
 * @Author: ddcoder <PERSON><PERSON><PERSON><PERSON>@movee.cn
 * @Date: 2023-03-28 15:22:55
 * @LastEditors: hgj
 * @LastEditTime: 2023-04-20 09:38:46
-->
<template>
  <div class="processing">
    <div class="processing-banner">
      <h1 class="title">中央厨房系统</h1>
      <p class="tips1 mb76">助力中央厨房企业精细化管理</p>
      <SubmitButton class="button">获取专属方案</SubmitButton>
    </div>
    <div class="processing-container">
      <div class="w-block">
        <div v-in-view-animate="['fly-up']" class="title">
          <span>产品价值</span>
          <span class="underline"></span>
        </div>
        <div v-in-view-animate="['fly-up']" class="up-box">
          <div class="up down">
            <img loading="lazy" src="../assets/processing/down.png" alt="货物损耗下降" />
            <span>货品损耗下降</span>
            <span class="percent">20%</span>
          </div>
          <div class="up down">
            <img loading="lazy" src="../assets/processing/down.png" alt="货物损耗下降" />
            <span>人力成本下降</span>
            <span class="percent">35%</span>
          </div>
          <div class="up">
            <img loading="lazy" src="../assets/processing/up.png" alt="货物损耗下降" />
            <span>计划效率提升</span>
            <span class="percent">90%</span>
          </div>
          <div class="up">
            <img loading="lazy" src="../assets/processing/up.png" alt="货物损耗下降" />
            <span>加工效率提升</span>
            <span class="percent">22%</span>
          </div>
          <div class="up">
            <img loading="lazy" src="../assets/processing/up.png" alt="货物损耗下降" />
            <span>订单效率提升</span>
            <span class="percent">85%</span>
          </div>
        </div>
      </div>
      <div class="g-block" id="xiadan">
        <div class="desc-box">
          <div v-in-view-animate="['fly-up']" class="desc-text">
            <div class="top">
              <img loading="lazy" src="../assets/processing/order-icon.png" alt="智能下单" />
              <span>智能下单</span>
            </div>
            <div class="content">
              <div class="text-box">
                <div class="img">
                  <img loading="lazy" src="~/assets/qsy/item_icon.png" alt="报餐助手" />
                </div>
                <div>
                  <span>报餐助手</span>
                  <span>学生家长自主缴费、报餐、请假，营养成分摄入量分析</span>
                </div>
              </div>
              <div class="text-box">
                <div class="img">
                  <img loading="lazy" src="~/assets/qsy/item_icon.png" alt="报餐助手" />
                </div>
                <div>
                  <span>线上商城</span>
                  <span>量身打造自主商城，客户商城下单，在线支付</span>
                </div>
              </div>
              <div class="text-box">
                <div class="img">
                  <img loading="lazy" src="~/assets/qsy/item_icon.png" alt="报餐助手" />
                </div>
                <div>
                  <span>智能定价</span>
                  <span>根据成本、市场价等，设置公式自动定价，符合市场需要</span>
                </div>
              </div>
            </div>
            <SubmitButton>免费试用</SubmitButton>
          </div>
          <div v-in-view-animate="['fly-up']" class="desc-img">
            <img loading="lazy" src="../assets/processing/intelligentOrdering.png" alt="智能下单" />
          </div>
        </div>
      </div>
      <div class="w-block" id="caipu">
        <div class="desc-box">
          <div v-in-view-animate="['fly-up']" class="desc-text">
            <div class="top">
              <img loading="lazy" src="../assets/processing/recipe-icon.png" alt="菜谱管理" />
              <span>菜谱管理</span>
            </div>
            <div class="content">
              <div class="text-box">
                <div class="img">
                  <img loading="lazy" src="~/assets/qsy/item_icon.png" alt="菜谱智能决策" />
                </div>
                <div>
                  <span>菜谱智能决策</span>
                  <span>菜品、菜谱毛利预估，营养成分分析，助力菜谱智能决策</span>
                </div>
              </div>
              <div class="text-box">
                <div class="img">
                  <img loading="lazy" src="~/assets/qsy/item_icon.png" alt="菜谱库" />
                </div>
                <div>
                  <span>菜谱库</span>
                  <span>打造企业菜谱库，快速制定符合要求的客户菜谱</span>
                </div>
              </div>
              <div class="text-box">
                <div class="img">
                  <img loading="lazy" src="~/assets/qsy/item_icon.png" alt="餐别餐标管理" />
                </div>
                <div>
                  <span>餐别餐标管理</span>
                  <span>餐别和餐标统一管理，简化菜谱作业</span>
                </div>
              </div>
            </div>
            <SubmitButton>免费试用</SubmitButton>
          </div>
          <div v-in-view-animate="['fly-up']" class="desc-img">
            <img loading="lazy" src="../assets/processing/recipe-bg.png" alt="菜谱管理" />
          </div>
        </div>
      </div>
      <div class="g-block" id="jihua">
        <div class="desc-box">
          <div v-in-view-animate="['fly-up']" class="desc-text">
            <div class="top">
              <img loading="lazy" src="../assets/processing/plan-icon.png" alt="计划管理" />
              <span>计划管理</span>
            </div>
            <div class="content">
              <div class="text-box">
                <div class="img">
                  <img loading="lazy" src="~/assets/qsy/item_icon.png" alt="需求自动汇总" />
                </div>
                <div>
                  <span>需求自动汇总</span>
                  <span>多方汇总多源头需求，实时掌控需求动态</span>
                </div>
              </div>
              <div class="text-box">
                <div class="img">
                  <img loading="lazy" src="~/assets/qsy/item_icon.png" alt=">MRP计算" />
                </div>
                <div>
                  <span>MRP计算</span>
                  <span>
                    通过多维度信息自动输入，一键计算生产的成品及半成品，
                    <br />
                    生产原材料情况
                  </span>
                </div>
              </div>
              <div class="text-box">
                <div class="img">
                  <img loading="lazy" src="~/assets/qsy/item_icon.png" alt="一键生成计划" />
                </div>
                <div>
                  <span>一键生成计划</span>
                  <span>
                    系统自动拆分，一键生成采购计划、生产计划，
                    <br />
                    计划效率提升90%
                  </span>
                </div>
              </div>
            </div>
            <SubmitButton>免费试用</SubmitButton>
          </div>
          <div v-in-view-animate="['fly-up']" class="desc-img">
            <img loading="lazy" src="../assets/processing/plan-bg.png" alt="计划管理" />
          </div>
        </div>
      </div>
      <div class="w-block" id="jiagong">
        <div class="desc-box">
          <div v-in-view-animate="['fly-up']" class="desc-text">
            <div class="top">
              <img loading="lazy" src="../assets/processing/process-icon.png" alt="加工管理" />
              <span>加工管理</span>
            </div>
            <div class="content">
              <div class="text-box">
                <div class="img">
                  <img loading="lazy" src="~/assets/qsy/item_icon.png" alt="生产过程管理" />
                </div>
                <div>
                  <span>生产流程可定义</span>
                  <span>领投料，完工入库是否分离，加工过程管理颗粒度自主把控</span>
                </div>
              </div>
              <div class="text-box">
                <div class="img">
                  <img loading="lazy" src="~/assets/qsy/item_icon.png" alt="生产过程管理" />
                </div>
                <div>
                  <span>生产过程管理</span>
                  <span>领料、投料、退料、完工，工艺路线工序过程监管，全程管控</span>
                </div>
              </div>
              <div class="text-box">
                <div class="img">
                  <img loading="lazy" src="~/assets/qsy/item_icon.png" alt="成本核算" />
                </div>
                <div>
                  <span>成本核算</span>
                  <span>同时满足营养餐、肉类分割、净菜加工多重业务模式成本核算</span>
                </div>
              </div>
            </div>
            <SubmitButton>免费试用</SubmitButton>
          </div>
          <div v-in-view-animate="['fly-up']" class="desc-img">
            <img loading="lazy" src="../assets/processing/process-bg.png" alt="加工管理" />
          </div>
        </div>
      </div>
      <div class="g-block" id="fenjian">
        <div class="desc-box">
          <div v-in-view-animate="['fly-up']" class="desc-text">
            <div class="top">
              <img loading="lazy" src="../assets/processing/smartSorting-icon.png" alt="智能分拣" />
              <span>智能分拣</span>
            </div>
            <div class="content">
              <div class="text-box">
                <div class="img">
                  <img loading="lazy" src="~/assets/qsy/item_icon.png" alt="需求自动汇总" />
                </div>
                <div>
                  <span>提效60%</span>
                  <span>智能电子秤同步分拣重量，免去手工统计</span>
                </div>
              </div>
              <div class="text-box">
                <div class="img">
                  <img loading="lazy" src="~/assets/qsy/item_icon.png" alt=">MRP计算" />
                </div>
                <div>
                  <span>杜绝错分、漏分</span>
                  <span>分拣进度可视化，分拣缺货标记，应对各种情况</span>
                </div>
              </div>
            </div>
            <SubmitButton>免费试用</SubmitButton>
          </div>
          <div v-in-view-animate="['fly-up']" class="desc-img">
            <img loading="lazy" src="../assets/processing/smartSorting-bg.png" alt="智能分拣" />
          </div>
        </div>
      </div>
      <div class="w-block" id="suyuan">
        <div class="desc-box">
          <div v-in-view-animate="['fly-up']" class="desc-text">
            <div class="top">
              <img loading="lazy" src="../assets/processing/foodSafety-icon.png" alt="食品溯源" />
              <span>食品溯源</span>
            </div>
            <div class="content">
              <div class="text-box">
                <div class="img">
                  <img loading="lazy" src="~/assets/qsy/item_icon.png" alt="源头追溯" />
                </div>
                <div>
                  <span>源头追溯</span>
                  <span>食材供应商是谁，进货时间，收货人是谁，检疫票证展示</span>
                </div>
              </div>
              <div class="text-box">
                <div class="img">
                  <img loading="lazy" src="~/assets/qsy/item_icon.png" alt="检测信息" />
                </div>
                <div>
                  <span>检测信息</span>
                  <span>农残检测是否超标，农残检测报告展示</span>
                </div>
              </div>
              <div class="text-box">
                <div class="img">
                  <img loading="lazy" src="~/assets/qsy/item_icon.png" alt="过程追溯" />
                </div>
                <div>
                  <span>过程追溯</span>
                  <span>生产过、原料产品转换、分拣、出库、配送过程展示</span>
                </div>
              </div>
            </div>
            <SubmitButton>免费试用</SubmitButton>
          </div>
          <div v-in-view-animate="['fly-up']" class="desc-img">
            <img loading="lazy" src="../assets/processing/foodSafety-bg.png" alt="食品溯源" />
          </div>
        </div>
      </div>
      <div class="g-block" id="fenxi">
        <div class="desc-box">
          <div v-in-view-animate="['fly-up']" class="desc-text">
            <div class="top">
              <img loading="lazy" src="../assets/processing/dataAnalysis-icon.png" alt="数据分析" />
              <span>数据分析</span>
            </div>
            <div class="content">
              <div class="text-box">
                <div class="img">
                  <img loading="lazy" src="~/assets/qsy/item_icon.png" alt="数据分析" />
                </div>
                <div>
                  <span>数据大屏</span>
                  <span>可视化数据大屏展示，数据实时掌控，树立企业形象</span>
                </div>
              </div>
              <div class="text-box">
                <div class="img">
                  <img loading="lazy" src="~/assets/qsy/item_icon.png" alt=">报表分析" />
                </div>
                <div>
                  <span>报表分析</span>
                  <span>
                    经营数据分析、生产数据分析、财务数据分析，
                    <br />
                    数据一目了然
                  </span>
                </div>
              </div>
            </div>
            <SubmitButton>免费试用</SubmitButton>
          </div>
          <div v-in-view-animate="['fly-up']" class="desc-img">
            <img loading="lazy" src="../assets/processing/dataAnalysis-bg.png" alt="数据分析" />
          </div>
        </div>
      </div>

      <div class="g-block advantage-bg">
        <p v-in-view-animate="['fly-up']" class="advantage-title">核心优势</p>
        <div v-in-view-animate="['fly-up']" class="advantage-box">
          <div class="advantage">
            <img loading="lazy" src="../assets/processing/advantage1.png" alt="货物损耗下降" />
            <p class="title2">贴合业务实际</p>
            <p class="tips2">从生产中来，到生产中去 真正有效解决实际生产痛点</p>
          </div>
          <div class="advantage">
            <img loading="lazy" src="../assets/processing/advantage2.png" alt="货物损耗下降" />
            <p class="title2">生产运营双核</p>
            <p class="tips2">不仅提供信息化解决方案 更提供运营解决方案</p>
          </div>
          <div class="advantage">
            <img loading="lazy" src="../assets/processing/advantage3.png" alt="货物损耗下降" />
            <p class="title2">资深专家团队</p>
            <p class="tips2">平均行业经验超30年 驻场指导生产%</p>
          </div>
          <div class="advantage">
            <img loading="lazy" src="../assets/processing/advantage4.png" alt="货物损耗下降" />
            <p class="title2">本地敏捷服务</p>
            <p class="tips2">8大本地化服务中心 提供针对性的全生命周期服务</p>
          </div>
        </div>
      </div>
      <div class="w-block img-list-box">
        <h3 v-in-view-animate="['fly-up']" class="img-list-title">应用场景</h3>
        <p v-in-view-animate="['fly-up']" class="img-list-desc">覆盖30W+生鲜配送企业业务场景</p>
        <div v-in-view-animate="['fly-up']" class="img-list">
          <a href="/xsyyc/" target="_blank" class="processing-link-block">
            <img loading="lazy" src="../assets/processing/imgList1.png" alt="" />
            <p>学生营养餐解决方案</p>
          </a>
          <a href="/roujg/" target="_blank" class="processing-link-block">
            <img loading="lazy" src="../assets/processing/imgList2.png" alt="" />
            <p>肉类加工与分割解决方案</p>
          </a>
          <a href="/jcjg/" target="_blank" style="display: block;">
            <img loading="lazy" src="../assets/processing/imgList3.png" alt="" />
            <p>净菜加工解决方案</p>
          </a>
        </div>
      </div>
    </div>
    <footer class="sidebar text-center g-block">
      <h3 v-in-view-animate="['fly-up']" class="sidebar__title">上万家客户信赖选择</h3>
      <CustomerScroll v-in-view-animate="['fly-up']" class="pb100"></CustomerScroll> 
    </footer>
      <div class="newMessage-container" v-if="newMessageList.length">
        <div class="newMessage-title">蔬东坡最新资讯</div>
        <Message :info-list="newMessageList" :max-length="15" />
        <div class="indicator"></div>
        <a class="newMessage-more" href="https://www.sdongpo.com/xueyuan/" target="_blank">查看更多 ></a>
      </div>
  </div>
</template>

<script setup>
import useAnchor from "../hooks/useAnchor";
import Message from "./message.vue";

import useInViewAnimate from '../hooks/inViewAnimate';
const vInViewAnimate = {
  mounted: useInViewAnimate,
}

useHead({
  title: "中央厨房管理系统_净菜加工系统_中央厨房管理软件-蔬东坡",
  meta: [
    {
      name: "description",
      content:
        "蔬东坡中央厨房管理系统,助力央厨业务管理数字升级,实现中央厨房生产集约化、操作智能化、生产高效化、食品绿色化、财账清晰化,提高央厨企业精细化管理能力",
    },
    { name: "keywords", content: "中央厨房管理系统,中央厨房系统,中央厨房管理软件,净菜加工,央厨SaaS 软件" },
  ],
  script: [
    {
      src: "https://hm.baidu.com/hm.js?d48f6da230615bef4b3765442ede582d",
      bodyClose: false,
      async: true,
      defer: true,
    },
  ],
});
useAnchor();
const mergeArraysAlternating = (arr1, arr2) => {
  const newArr = [];
  const existingIds = new Set();
  let i = 0,
    j = 0;
  const len1 = arr1.length;
  const len2 = arr2.length;
  while (i < len1 || j < len2) {
    // 处理arr1
    if (i < len1) {
      const obj1 = arr1[i];
      if (!existingIds.has(obj1.id)) {
        newArr.push(obj1);
        existingIds.add(obj1.id);
        i++;
      } else {
        i++;
      }
    }
    // 处理arr2
    if (j < len2) {
      let added = false;
      while (j < len2 && !added) {
        const obj2 = arr2[j];
        if (!existingIds.has(obj2.id)) {
          newArr.push(obj2);
          existingIds.add(obj2.id);
          added = true;
        } else {
          j++;
        }
      }
    }
  }
  return newArr.slice(0, 10);
};
// 最新资讯
const newMessageList = ref([]);
const res1 = await getNewMessageList({
  order: "desc",
  per_page: 10,
  orderby: "date",
  search: ["中央厨房"],
});
const res2 = await getNewMessageList({
  order: "desc",
  per_page: 10,
  orderby: "date",
  search: ["中央厨房系统"],
});
newMessageList.value = mergeArraysAlternating(res1, res2);
// console.log("合并后的数据:", newMessageList.value);
</script>
<style>
.processing .sidebar .overflow-hidden {
  width: 1900px !important;
}
</style>

<style lang="postcss" scoped>
@import url(~/assets/style/ds.css);
@import url(~/assets/style/tool.css);
.newMessage-container {
  margin: 40px auto 80px; 
}
.processing {
  min-width: 1300px;

  .processing-banner {
    width: 100%;
    display: flex;
    flex-direction: column;
    align-items: center;
    color: #ffffff;
    background: url(../assets/processing/banner.png) no-repeat center center;
    height: 694px;
    background-size: cover;

    .title {
      margin-top: 200px;
      font-weight: 400;
      font-size: 56px;
    }

    .tips1 {
      margin-top: 53px;
      font-weight: 400;
      font-size: 32px;
    }

    .mb76 {
      margin-bottom: 76px;
    }
  }

  .processing-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;

    .title {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      margin-top: 82px;
      font-weight: 500;
      font-size: 24px;
    }

    .underline {
      width: 142px;
      height: 3px;
      margin-top: 10px;
      background: #2977fe;
      border-radius: 5px;
    }

    .up-box {
      width: 100%;
      margin-top: 53px;
      margin-bottom: 90px;
      display: flex;
      align-items: center;
      justify-content: center;

      .up {
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        height: 220px;
        width: 220px;
        background: linear-gradient(180deg, #ffffff -32.39%, #f1f9ff 132.39%);
        box-shadow: 5px 5px 5px rgba(165, 181, 214, 0.2);
        border-radius: 5px;
        margin-right: 30px;
        font-weight: 400;
        font-size: 16px;
        color: #333333;

        span {
          margin-top: 8px;
        }

        .percent {
          margin-top: 12px;
          font-weight: 500;
          font-size: 24px;
          line-height: 24px;
        }

        img {
          width: 68px;
          height: 92px;
        }
      }
    }

    .desc-box {
      display: flex;
      justify-content: center;
      margin-top: 70px;
      margin-bottom: 70px;

      .desc-text {
        color: #333333;
        width: 460px;
        margin-right: 30px;
        .top {
          display: flex;
          align-items: center;
          margin-bottom: 30px;

          span {
            font-weight: 500;
            font-size: 24px;
            line-height: 24px;
          }

          img {
            width: 34px;
            height: 34px;
          }
        }

        .content {
          font-size: 16px;
          font-weight: 400;

          .text-box {
            display: flex;
            margin-bottom: 30px;

            span {
              display: block;
            }
            .img {
              margin-top: 5px;
              margin-right: 5px;
              img {
                width: 15px;
                height: 15px;
              }
            }
          }

          .text-box:last-of-type {
            margin-bottom: 50px;
          }
        }
      }

      .desc-img {
        img {
          width: 700px;
          object-fit: cover;
        }
      }
    }
    .img-list-box {
      display: flex;
      flex-direction: column;
      align-items: center;
      padding-top: 70px;
      padding-bottom: 150px;
      color: #333333;
      .img-list-title {
        text-align: center;
        margin-bottom: 10px;
        font-weight: 500;
        font-size: 24px;
      }
      .img-list-desc {
        text-align: center;
        color: #666666;
        font-size: 16px;
        font-weight: 400;
      }
      .img-list {
        margin-top: 40px;
        display: flex;
        justify-content: center;
        align-items: center;
        a {
          margin-right: 40px;
          width: 250px;
          position: relative;
          img {
            width: 250px;
            height: 300px;
          }
          p {
            position: absolute;
            color: #ffffff;
            width: 250px;
            bottom: 30px;
            left: 0;
            text-align: center;
          }
        }
      }
    }
  }

  .w-block {
    width: 100%;
    background: #ffffff;
  }

  .g-block {
    width: 100%;
    background: #f9fbff;
  }
  .advantage-bg {
    background-size: cover;
    background: url(../assets/processing/advantage-bg.png) no-repeat center center;
    .advantage-title {
      color: #333333;
      font-size: 24px;
      font-weight: 500;
      text-align: center;
    }
  }

  .button {
    width: 200px;
    font-size: 20px;
    height: 50px;
    line-height: 50px;
    text-align: center;
    font-weight: 400;
    padding: 0;
  }
  .advantage-box {
    width: 100%;
    margin-top: 80px;
    margin-bottom: 80px;
    display: flex;
    align-items: center;
    justify-content: center;
    .advantage {
      color: #333333;
      font-size: 20px;
      font-weight: 400px;
      width: 230px;
      height: 250px;
      background: #ffffff;
      display: flex;
      flex-direction: column;
      align-items: center;
      position: relative;
      margin-right: 30px;
      padding-top: 90px;
      .title2 {
        color: #333333;
        font-size: 20px;
        font-weight: 500;
      }
      p {
        margin-bottom: 30px;
      }
      .tips2 {
        width: 180px;
        text-align: center;
        font-size: 14px;
      }
      img {
        position: absolute;
        top: -30px;
        left: 50%;
        transform: translateX(-50%);
        width: 181px;
        height: 99px;
      }
    }
  }
  .tips3 {
    color: #666666;
    font-size: 24px;
    font-weight: 400;
    text-align: center;
  }
  .sidebar {
    &__title {
      color: #333333;
      font-size: 40px;
      padding-top: 86px;
      padding-bottom: 42px;
    }
  }
  .pb100 {
    padding-bottom: 100px;
  }

  @keyframes fly-up {
    0% {
      transform: translateY(100px);
      opacity: 0;
    }

    100% {
      transform: translateY(0);
      opacity: 1;
    }
  }

  .fly-up {
    animation-name: fly-up;
    animation-fill-mode: both;
    animation-duration: 0.5s;
  }

  @keyframes scale-large {
    0% {
      transform: scale(0.2);
      opacity: 0;
    }

    100% {
      transform: scale(1);
      opacity: 1;
    }
  }

  .scale-large {
    animation-name: scale-large;
    animation-fill-mode: both;
    animation-duration: 0.5s;
  }

  @keyframes fade-in {
    0% {
      opacity: 0;
    }

    100% {
      opacity: 1;
    }
  }

  .fade-in {
    animation-name: fade-in;
    animation-fill-mode: both;
    animation-duration: 0.5s;
  }

  .animate-element {
    opacity: 0;
  }
}
</style>
>
