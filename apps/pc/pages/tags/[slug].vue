<template>
    <div class="outer_container" v-if="artcleList && artcleList.length">
      <div class="box"></div>
      <div class="all_container">
        <div class="header">
          <a href="/" class="item">蔬东坡</a>
          <span class="add_margin">&gt;</span>
          {{ title || '' }}
        </div>
        <div class="main_outer">
          <div class="main_container">
          <h1 class="header_text">{{ title || '' }}</h1>
          <div class="main" v-for="item in artcleList" :key="item.id">
            <!-- <div class="text_container">
              <div class="header_text">{{ item.title?.rendered }}</div>
              <p>{{ item.content?.rendered }}</p>
            </div> -->
            <div class="text_container">
              <!-- <div class="title">{{ item.title?.rendered }}</div> -->
              <a class="title" :href="`https://sdongpo.com/xueyuan/c-${item.id}.html`">{{ item?.title?.rendered }}</a>
              <pre class="text_detail text-detail-pre-line">
                <div v-html="replaceLink(item?.excerpt?.rendered || '')"></div>
              </pre>
            </div>
            
          </div>
          </div>
          <div class="left">
            <div class="left_top">
              <div class="top_container">
                 <img src="~/assets/artcle_collection/left_top_img.png" alt="">
              </div>
              <HotTags class="hot_tags"></HotTags>
            </div>
          </div>
        </div>
      </div>
    </div>
  </template>
  
  <script setup>
  import { useRoute } from 'vue-router'
  const tag = useState('tag');
  
  let title = ''
  let artcleList = ref([]);
  const route = useRoute()
  // const id = route.query.id
  let name = route?.params?.slug || '';
  const tagName = tag?.value?.name
  tagName?.forEach((item) => {
    if (item.slug === name) {
      title = item.name
    }
  });

  artcleList = await getArtcleCollection({name});
  const replaceLink = (html) => {
  return html.replace(/https:\/\/xueyuan\.sdongpo\.com\//g, 'https://sdongpo.com/xueyuan/');
};

  </script>
<style scoped>
@import url(~/assets/style/tool.css);
  .outer_container {
    background-color: #F7F8FC;
    .all_container {
      max-width: 1140px;
      margin: 0 auto;
    }
    .main_outer {
      display: flex;
    }
    .main_container {
      background-color: #ffffff;
      padding: 0 20px 0 20px;
      .header_text {
        font-size: 28px;
        font-weight: 600;
        color: #333;
        line-height: 57px;
        margin: 30px 0 20px 0;
      }

      .text_container {
        width: 780px;
        padding: 20px 20px 0 20px;
        border: 2px #f2f4f6 solid;
        border-radius: 5px;
        margin-bottom: 20px;
        .title {
          font-size: 24px;
          color: #333333;
          font-weight: 500;
        }
      }
    }
    .left {
      width: 298px;
      padding-left: 30px;
      .top_container {
      }
      .hot_tags {
        width: 270px;
        margin-top: 20px; 
      }
      /deep/ .hot_tags {
        background-color: #ffffff;
      }
    }
    .box {
      width: 100%;
      height: 94px;
      background-color: #2277f6;
    }
    .header {
      margin: 30px 0 0 0px;
      padding: 30px 0 20px 0;
      color: #666;
      font-size: 18px;
      .add_margin {
        margin: 0 10px;
      }
      .item {
      cursor: pointer;
      &:hover {
        color: #2977fe;
      }
    }
    }
  }

  .text_detail{
    /deep/ div,
    p {
      margin: 10px 0;
      color: #666;
      font: 18px PingFang SC-Regular, PingFang SC;
      line-height: 33px;
    }
    /deep/ h3 {
      margin: 10px 0;
      color: #3c3c3c;
      font-size: 16px;
      font-weight: bold;
    }
    /deep/ img {
      max-width: 650px;
      margin: 10px auto;
    }
  }
</style>
  