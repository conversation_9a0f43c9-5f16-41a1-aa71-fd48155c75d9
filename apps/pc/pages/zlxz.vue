<!--
 * @Description: 行业会议页面
 * @Date: 2023-06-07 15:51:57
 * @LastEditors: fc <EMAIL>
 * @LastEditTime: 2023-07-07 17:01:20
 * @FilePath: /shudongpo-website/apps/pc/pages/zlxz.vue
-->
<template>
  <div class="hymt">
    <div class="news_header">
      <div class="news_header__box">
        <h1 class="news_header__title">资料下载</h1>
        <div class="news_header__line"></div>
        <h2 class="news_header__desc">超多实用模板，即下即用</h2>
      </div>
    </div>
    <StudyCenterTabs>
      <div class="g-block">
        <div class="down_box">
            <div
              class="article"
              v-in-view-animate="['fly-up']"
              v-for="(item, index) in dataList"
              :key="index"
            ><div class="down_title" @click="jumpDetail(item?.src,item.post_title)">
              <!-- <p class="img_title">{{ item.post_title }}</p> -->
              <img src="~/assets/xueyuan/zlxz_illustration.png" alt="资料下载" >
            </div>
              <div class="txt-box">
                <p class="title text-two-line" @click="jumpDetail(item?.src,item.post_title)">
                  {{ item.post_title }}
                </p>
                <div class="downbox">
                  <p class="article__desc">{{ item.post_date_format }}</p>
                  <div class="down_click" @click="jumpDetail(item?.src,item.post_title)">点击下载</div>
                </div>
              </div>
            </div>
        </div>
      </div>
    </StudyCenterTabs>
      <div class="modal__mask" v-show="showModal">
    <div class="modal__main">
      <div class="modal__header">
        <img class="logoimg" src="~/assets/common/logo.png" alt="logo" />
        <span class="divider">|</span>
        <span>生鲜SaaS ERP</span>
        <img @click="handleCloseModal" class="modal__close"
          src="https://website-image.sdongpo.com/website/icon--close.png" alt="关闭" />
      </div>
      <div class="modal__content form-modal">
        <TheForm ref="form" @on-change="handleChange"></TheForm>
      </div>
      <div class="modal__btn" @click="handleSaveFormData">点击提交</div>
       <div class="modal__tips">请填写相关信息下载文件</div>
    </div>
  </div>
  </div>
</template>
<script setup>
useHead({
  title: '蔬东坡-资料下载',
  meta: [
    { name: 'description', content: '蔬东坡资料下载页面包含生鲜配送行业管理方面内容和资料，致力于为生鲜人提供一个学习和成长的平台。' },
    { name: 'keywords', content: '	资料下载，蔬东坡' },
  ],
});

  let showModal = ref(false);
  let url = ref(null);
  let fileName = ref(null);
  let formData = {};
  const form = ref(null);
  const handleChange = data => {
    formData = data;
  };
  const handleCloseModal = () => {
    form.value.resetForm();
    showModal.value = false
  };
  const handleSaveFormData = async () => {
    const validate = form.value.validate();
    if (!validate.valid) {
      showError(validate.error);
      return;
    }
    currentModalType.value = ModalType.datum;
    const { data } = await saveFormData(formData);
    const res = JSON.parse(data.value);
    // showSuccess(res.message);
    if (res.status === 'success') {
      window.open(url.value)
      handleCloseModal()
    }
  };
let dataList = ref([]);
const getArticleList = async () => {
  const res = await getStudyCenterMedia('资料下载')
  if (res.status) {
    dataList.value = res.data || []
  }
};
getArticleList();
const jumpDetail = (link,name) => {
  if(!link) return
  currentModalType.value = ModalType.datum
  showModal.value = true
  fileName.value = name
  url.value = link
}
</script>
<style lang="postcss" scoped>
@import url(~/assets/style/xueyuan.css);
@import url(~/assets/style/zlxz.css);
</style>
