<!--
 * @Author: ddcoder <PERSON><PERSON><PERSON><PERSON>@movee.cn
 * @Date: 2023-03-28 15:22:55
 * @LastEditors: hgj
 * @LastEditTime: 2023-05-23 16:12:08
-->
<template>
  <div class="home">
    <div class="header">
      <div class="header-container">
        <div class="title">
          <h1>生鲜食材</h1>
          供应链数字化开创者
        </div>
        <p class="sub-title">让生鲜人每天多睡两小时</p>
        <div class="form-box">
          <p class="form-title">申请免费体验</p>
          <div class="padleft">
            <div class="form-item">
              <div class="label">姓名</div>
              <div class="field"><input v-model="formData.username" placeholder="请输入您的姓名" /></div>
            </div>
            <div class="form-item">
              <div class="label">电话</div>
              <div class="field"><input v-model="formData.phone" placeholder="请输入您的电话号码" /></div>
            </div>
          </div>
          <div class="submit-btn" @click="handleSumit">申请体验</div>
        </div>
      </div>
    </div>
    <div class="liucheng" :class="{ 'liucheng--show': isLiuchengvisible }">
      <div class="liucheng-main">
        <div class="liucheng-caigou liucheng-item">
          <img loading="lazy" src="../assets/index/yewuliucheng-caigou.png" alt="采购入库" />
          <div class="tip arrow-right top-left">
            <div class="tip-title">采购入库</div>
            <div class="tip-desc">
              <p>1.数据自动汇总，并计算库存</p>
              <p>2.采购智能称一键收货</p>
            </div>
          </div>
        </div>
        <div class="liucheng-cangku liucheng-item">
          <img loading="lazy" src="../assets/index/yewuliucheng-cangku.png" alt="采购入库" />
          <div class="tip arrow-left top-right">
            <div class="tip-title">仓库管理</div>
            <div class="tip-desc">
              <p>1.库房自动预警，提醒缺货、过期商品</p>
              <p>2.库区库位规范化管理，有效避免丢货、损耗</p>
            </div>
          </div>
        </div>
        <div class="liucheng-xiadan liucheng-item">
          <img loading="lazy" src="../assets/index/yewuliucheng-xiadan.png" alt="客户下单" />
          <div class="tip arrow-right left-middle">
            <div class="tip-title">客户下单</div>
            <div class="tip-desc">
              <p>1.自动接单、全渠道覆盖</p>
              <p>2.语音下单、常用清单等便捷化下单方式</p>
              <p>3.灵活的营销政策</p>
            </div>
          </div>
        </div>
        <div class="liucheng-peisong liucheng-item">
          <img loading="lazy" src="../assets/index/yewuliucheng-shuju.png" ref="target" alt="数据大脑" />
          <div class="tip arrow-down center">
            <div class="tip-title">数据大脑</div>
            <div class="tip-desc">
              <p>1.多维度损耗报表</p>
              <p>2.采购价格走势图</p>
              <p>3.利润报表实时出</p>
            </div>
          </div>
        </div>
        <div class="liucheng-fenjian liucheng-item">
          <img loading="lazy" src="../assets/index/yewuliucheng-fenjian.png" alt="分拣投筐" />
          <div class="tip arrow-left right-middle">
            <div class="tip-title">智能分拣</div>
            <div class="tip-desc">
              <p>1.智能称将商品实重实时上传</p>
              <p>2.智能化标签，避免错分漏分</p>
            </div>
          </div>
        </div>
        <div class="liucheng-shuju liucheng-item">
          <img loading="lazy" src="../assets/index/yewuliucheng-peisong.png" alt="配送调度" />
          <div class="tip arrow-right bottom-left">
            <div class="tip-title">配送调度</div>
            <div class="tip-desc">
              <p>1.可视化一键物流排线</p>
              <p>2.发货单模板完全自定义</p>
              <p>3.发货单批量打印</p>
            </div>
          </div>
        </div>
        <div class="liucheng-suyuan liucheng-item">
          <img loading="lazy" src="../assets/index/yewuliucheng-suyuan.png" alt="食安溯源" />
          <div class="tip arrow-left bottom-right">
            <div class="tip-title">食安溯源</div>
            <div class="tip-desc">
              <p>1.区块链+大数据，一品一码</p>
              <p>2.扫码溯源，保障食品安全</p>
            </div>
          </div>
        </div>
      </div>
      <SubmitButton class="liucheng-btn" />
    </div>
    <div class="feature-tab-container bg-gray">
      <h2 class="feature-tab-title">蔬东坡系统优势</h2>
      <div class="feature-tab">
        <div class="feature-tab__item" :class="{ active: activeTab === 'sunhao' }" @mouseover="changeTab('sunhao')">
          <div class="feature-tab__title">商品损耗下降</div>
          <div class="feature-tab__desc">
            <img loading="lazy" src="../assets/index/icon-sunhao.png" alt="商品损耗下降20%" />
            20%
          </div>
        </div>
        <div class="feature-tab__item" :class="{ active: activeTab === 'fenjian' }" @mouseover="changeTab('fenjian')">
          <div class="feature-tab__title">分拣速度上升</div>
          <div class="feature-tab__desc">
            <img loading="lazy" src="../assets/index/icon-fenjian.png" alt="分拣速度上升60%" />
            60%
          </div>
        </div>
        <div class="feature-tab__item" :class="{ active: activeTab === 'dingdan' }" @mouseover="changeTab('dingdan')">
          <div class="feature-tab__title">订单效率上升</div>
          <div class="feature-tab__desc">
            <img loading="lazy" src="../assets/index/icon-dingdan.png" alt="订单效率上升90%" />
            90%
          </div>
        </div>
        <div
          class="feature-tab__item"
          :class="{ active: activeTab === 'zhunquelv' }"
          @mouseover="changeTab('zhunquelv')"
        >
          <div class="feature-tab__title">准确率上升</div>
          <div class="feature-tab__desc">
            <img loading="lazy" src="../assets/index/icon-zhunquelv.png" alt="准确率上升99.9%" />
            99.9%
          </div>
        </div>
        <div class="feature-tab__item" :class="{ active: activeTab === 'lirun' }" @mouseover="changeTab('lirun')">
          <div class="feature-tab__title">净利润</div>
          <div class="feature-tab__desc">
            <img loading="lazy" src="../assets/index/icon-lirun.png" alt="净利润3-5%" />
            3-5%
          </div>
        </div>
      </div>
      <div class="feature-tab__content">
        <RichSection
          :titleBg="false"
          :btn="false"
          v-show="activeTab === 'sunhao'"
          title="进销存管理 严控损耗"
          :desc="[
            '精细化库区、库位、货架管理\n可有效减少90%的找货时间',
            '扫码收获入库+扫码盘点\n轻松一扫，盘点效率提升2倍',
            '移动操作，无纸化管理\n随时随地，掌握库房情况',
          ]"
        >
          <template #icon>
            <img loading="lazy" src="../assets/index/icon-sunhao.png" alt="进销存管理 严控损耗" />
          </template>
          <template #img>
            <img loading="lazy" src="../assets/index/sunhao.png" slot="img" alt="进销存管理 严控损耗" width="687" />
          </template>
        </RichSection>
        <RichSection
          :titleBg="false"
          :btn="false"
          v-show="activeTab === 'fenjian'"
          title="分拣称重 提效60%"
          :desc="[
            '标品一键分拣，自动打单称重\n数据通传生鲜配送系统后台，效率提高60%',
            '分拣状态查询，杜绝错分漏分\n分拣进度可视化，在家也可做管理',
            '多种分拣方式可供选择\n支持商品分拣、客户分拣、路线分拣...',
          ]"
        >
          <template #icon>
            <img loading="lazy" src="../assets/index/icon-fenjian.png" alt="分拣称重 提效60%" />
          </template>
          <template #img>
            <img loading="lazy" src="../assets/index/fenjian.png" slot="img" alt="分拣称重 提效60%" width="662" />
          </template>
        </RichSection>
        <RichSection
          :titleBg="false"
          :btn="false"
          v-show="activeTab === 'dingdan'"
          title="快速下单 一键完成"
          :desc="[
            '订单处理，自动汇总\n节省人工统计时间',
            '补单/改单，及时调整\n采购/供应商可手机实时查看及调整',
            '联动库存，计算损耗\n通过生鲜配送系统进行更为精确的采购预测',
          ]"
        >
          <template #icon>
            <img loading="lazy" src="../assets/index/icon-dingdan.png" alt="快速下单 一键完成" />
          </template>
          <template #img>
            <img loading="lazy" src="../assets/index/xiadan.png" slot="img" alt="快速下单 一键完成" width="689" />
          </template>
        </RichSection>
        <RichSection
          :titleBg="false"
          :btn="false"
          v-show="activeTab === 'zhunquelv'"
          title="全流程智能管控"
          :desc="[
            '“生鲜大脑”——生鲜配送系统，实时掌握\n采购、库房、配送、手动报损等数据',
            '分拣大屏，进度可见\n错分漏分预警提醒，分拣进度实时把控',
            '数据大屏，精准分析\n订单、商品、交易、配送、多维度分析',
          ]"
        >
          <template #icon>
            <img loading="lazy" src="../assets/index/icon-zhunquelv.png" alt="全流程智能管控" />
          </template>
          <template #img>
            <img loading="lazy" src="../assets/index/zhunquelv.png" slot="img" alt="全流程智能管控" width="736" />
          </template>
        </RichSection>
        <RichSection
          :titleBg="false"
          :btn="false"
          v-show="activeTab === 'lirun'"
          title="多维度统计分析"
          :desc="[
            '经营状况，随时呈现\n商品、订单的销售/退货情况清晰展现',
            '财务数据，自动生成\n每笔订单均有据可查，收款记录清晰明了',
            '全流程损耗统计\n通过生鲜配送系统把控各环节，降低20%损耗成本',
          ]"
        >
          <template #icon>
            <img loading="lazy" src="../assets/index/icon-lirun.png" alt="多维度统计分析" />
          </template>
          <template #img>
            <img loading="lazy" src="../assets/index/lirun.png" slot="img" alt="多维度统计分析" width="623" />
          </template>
        </RichSection>
      </div>
    </div>
    <div class="scene-container">
      <div class="scene-title">聚焦垂直行业，覆盖30W+生鲜企业业务场景</div>
      <div class="scene-sub-title">为每一家生鲜企业，定制专属智能化生鲜配送系统</div>
      <el-carousel indicator-position="none" height="22.65625vw" :autoplay="false" arrow="always">
        <el-carousel-item v-for="item in sceneGroup" :key="item">
          <div class="scene-list">
            <div v-for="(scene, index) in item" :key="index" class="scene-item">
              <div class="scene-item__title-show">{{ scene.title }}</div>
              <img loading="lazy" :alt="scene.title" :src="scene.img" />
              <a class="scene-mask scene-mask-block" :href="scene.url" target="_blank">
                <div class="scene-item__title">
                  {{ scene.title }}
                  <div>解决方案</div>
                </div>
                <div class="scene-item__desc">
                  <p v-for="(desc, index) in scene.desc" :key="index">{{ desc }}</p>
                </div>
              </a>
            </div>
          </div>
        </el-carousel-item>
      </el-carousel>
    </div>

    <div class="gonglue-container bg-gray">
      <div class="gonglue-content">
        <div class="gonglue-title">强运营实战攻略</div>
        <div class="gonglue-sub-title">从生鲜配送到运营干货，生鲜老板必备</div>
        <div class="gonglue-list">
          <div class="gonglue-item">
            <div class="gonglue-item-img">
              <SubmitButton class="gonglue-item-btn">点击获取</SubmitButton>
              <img loading="lazy" src="../assets/index/gonglue-1.png" alt="生鲜研究院" />
            </div>
            <div class="gonglue-item-title">生鲜研究院</div>
            <div class="gonglue-item-sub-title">已帮助10000+企业提升20%利润</div>
            <div class="gonglue-item-desc">
              <p>由蔬东坡发起并成立生鲜研究院</p>
              <p>集结行业精英为企业提供综合学习平台</p>
            </div>
          </div>
          <div class="gonglue-item">
            <div class="gonglue-item-img">
              <SubmitButton class="gonglue-item-btn">点击获取</SubmitButton>
              <img loading="lazy" src="../assets/index/gonglue-2.png" alt="行业白皮书" />
            </div>
            <div class="gonglue-item-title">行业白皮书</div>
            <div class="gonglue-item-sub-title">3000家企业管理经验</div>
            <div class="gonglue-item-desc">
              <p>全国数千家企业管理实践经验编制</p>
              <p>帮助生鲜企业通过精益化管理降本增效</p>
            </div>
          </div>
          <div class="gonglue-item">
            <div class="gonglue-item-img">
              <SubmitButton class="gonglue-item-btn">点击获取</SubmitButton>
              <img loading="lazy" src="../assets/index/gonglue-3.png" alt="实操干货" />
            </div>
            <div class="gonglue-item-title">实操干货</div>
            <div class="gonglue-item-sub-title">2.9G干货，1000+模版</div>
            <div class="gonglue-item-desc">
              <p>企业制度/架构，岗位职责，薪酬绩效</p>
              <p>企业文化，仓储设计，品牌建设等干货</p>
            </div>
          </div>
          <div class="gonglue-item">
            <div class="gonglue-item-img">
              <SubmitButton class="gonglue-item-btn">点击获取</SubmitButton>
              <img
                class="gonglue-item-img-link"
                loading="lazy"
                src="../assets/index/gonglue-4.png"
                alt="生鲜云图"
                @click="onOpen('https://jinshuju.net/f/KQIxIX')"
              />
            </div>
            <div class="gonglue-item-title">生鲜云图</div>
            <div class="gonglue-item-sub-title">行业发展扩张终极指南</div>
            <div class="gonglue-item-desc">
              <p>从初创到转型所有企业经营干货</p>
              <p>从业者必备权威宝典</p>
            </div>
          </div>
        </div>
      </div>
    </div>

    <div class="kehurenke-container">
      <div class="kehurenke-title">来自客户的认可</div>
      <div class="kehurenke-sub-title">每10家生鲜配送企业，就有7家选择蔬东坡</div>
      <el-carousel
        height="18.5vw"
        ref="carouselRef"
        indicator-position="none"
        :autoplay="false"
        arrow="always"
        @change="handleCarouselChange"
      >
        <el-carousel-item v-for="item in kehuRenkeList" :key="item.title">
          <div class="kehurenke-item">
            <video
              id="video"
              class="kehurenke-item-video"
              width="563"
              height="275"
              @pause="handlePause"
              @ended="handleEnded"
              controls=""
            >
              <source :src="item.vedio" preload="none" type="video/mp4" />
            </video>
            <div>
              <div class="kehurenke-item-title">
                <!-- <img loading="lazy" class="kehurenke-item-logo" :src="item.logo" :alt="item.title" /> -->
                <a v-if="item.url" :href="item.url" target="_blank">{{ item?.title || '' }}</a>
              </div>
              <div class="kehurenke-item-desc">
                {{ item?.desc || '' }}
              </div>
            </div>
          </div>
        </el-carousel-item>
      </el-carousel>
      <div ref="videoRef"></div>
    </div>

    <div class="kehu-list bg-gray">
      <div class="kehu-list-main">
        <div class="kehu-list-title">蔬东坡-上万家客户值得依赖的选择</div>
        <CustomerScroll></CustomerScroll>
      </div>
    </div>

    <div class="article-container">
      <div class="article-title">蔬东坡最新动态</div>
      <el-carousel ref="carousel" indicator-position="outside" height="13vw" :autoplay="false" arrow="always">
        <el-carousel-item v-for="item in articleGroup" :key="item">
          <div class="article-list">
            <div v-for="(article, index) in item" :key="index" class="article-item" @click="goPage(article)">
              <img v-if="article.post_img" loading="lazy" :alt="article.post_title" :src="article.post_img" />
              <a v-if="article.ID" :href="`/xueyuan/c-${article.ID}.html`" target="_blank" class="article-content-title">
                <span>{{ article?.post_title || '' }}</span>
              </a>
            </div>
          </div>
        </el-carousel-item>
      </el-carousel>
      <div class="indicator"></div>
      <a class="more" href="https://www.sdongpo.com/xueyuan/" target="_blank">查看更多 ></a>
    </div>

    <div class="newMessage-container bg-gray" v-if="newMessageList.length">
      <div class="newMessage-title">蔬东坡最新资讯</div>
      <div class="newMessage-info">
        <div class="newMessage-info-list">
          <Message :info-list="newMessageList" :max-length="15" class="newMessage-info-list-ref"/>
          <div class="newMessage-more">
            <a  class="newMessage-more-text" href="https://www.sdongpo.com/xueyuan/" target="_blank">查看更多 ></a>
          </div>
        </div>
      </div>
      <!-- <div class="indicator"></div> -->
    </div>
  </div>
</template>
<script setup>
import { useElementVisibility } from "@vueuse/core";
import sceneShitangPeisong from "../assets/index/fangan-shitangpeisong.png";
import sceneJiudianPeisong from "../assets/index/fangan-jiudianpeisong1.png";
import sceneShitangChengbao from "../assets/index/fangan-shitangchengbao.png";
// import sceneShitangGongyinglian from '../assets/index/fangan-shitanggongyinglian.png';
import sceneZhangyangChufang from "../assets/index/fangan-zhongyangchufang.png";
import sceneRouleiJiagong from "../assets/index/fangan-rouleijiagong.png";
import sceneJingcaiJiagong from "../assets/index/fangan-jingcaijiagong.png";
import { ModalType } from "~/composables";
import Message from "./message.vue";

import { ref } from 'vue';

// 最新资讯
let newMessageList = ref([]);
const data = {
  order: "desc",
  orderby: "date",
  per_page: 10,
};

try {
  const res = await getNewMessageList(data);
  if (res) {
    newMessageList.value = res.map(item => {
      // 确保每个 item 不包含 Symbol 键
      return {
        ...item,
        level: item.level ? String(item.level) : item.level, // 将 Symbol 或其他不可序列化的值转换为可序列化的类型
        message: item.message ? String(item.message) : item.message,
      };
    });
  }
} catch (error) {
  console.error("Error fetching new messages:", error);
}

const target = ref(null);
const isLiuchengvisible = ref(false);
const videoRef = ref(null);

let canAutoPlay = false;
const handlePause = () => {
  canAutoPlay = false;
};
const carouselRef = ref(null);
const handleEnded = () => {
  console.log("handleEnded-----");
  carouselRef.value.next();
};
const goPage = article => {
  window.location.href = `/xueyuan/c-${article.ID}.html`;
};
onMounted(() => {
  const scrollContainer = document.querySelector("#__nuxt");
  const visiable = useElementVisibility(target, {
    scrollTarget: scrollContainer,
  });
  const videoVisiable = useElementVisibility(videoRef, {
    scrollTarget: scrollContainer,
  });
  const videos = Array.from(document.querySelectorAll(".kehurenke-container video"));
  // 初始声音调整为系统声音的30%
  videos.forEach(video => {
    video.volume = 0.3;
  });
  scrollContainer.addEventListener("scroll", () => {
    isLiuchengvisible.value = visiable.value;
    try {
      if (videoVisiable.value && canAutoPlay) {
        if (videos.every(item => item.paused)) {
          console.log("canAutoPlay", canAutoPlay);
          console.log("videoVisiable", videoVisiable.value);
          console.log("videos", videos);
          videos[0].play();
        }
      }
    } catch (err) {
      console.log(err);
    }
  });
});

useHead({
  title: "蔬东坡生鲜配送软件系统,蔬菜食材配送系统-蔬东坡",
  meta: [
    {
      name: "description",
      content:
        "蔬东坡提供专业的生鲜供应链SaaS解决方案，包括生鲜配送系统、蔬菜配送软件、食材配送软件等，为超过10000家生鲜配送企业提供数智服务",
    },
    { name: "keywords", content: "生鲜配送系统,生鲜配送软件,蔬东坡" },
  ],
  script: [
    {
      src: "https://hm.baidu.com/hm.js?5fa4f2fc5624f544affb211eab296f46",
      bodyClose: false,
      async: true,
      defer: true,
    },
  ],
});

const formData = reactive({
  username: "",
  phone: "",
});
const handleSumit = async () => {
  if (!phonePattern.test(formData.phone.trim())) {
    showError("请填写正确手机号");
    return;
  }
  currentModalType.value = ModalType.common;
  const { data } = await saveFormData(formData);
  const res = JSON.parse(data.value);
  if (res.status === "success") {
    formData.username = "";
    formData.phone = "";
    showSuccess(res.message);
  } else {
    showError(res.message);
  }
};

const activeTab = ref("sunhao");
const changeTab = tab => {
  activeTab.value = tab;
};

const articleGroup = [[]];
let groupIndex = 0;
try {
  console.time("artile");
  const articleRes = await getArticleList();
  console.timeEnd("artile");
  console.log("----------------------");
  if (articleRes && articleRes.data && articleRes.data.list)
    articleRes.data.list.forEach((item, index) => {
      item.post_img = item.thumbnail ? `${item.thumbnail}?x-oss-process=image/resize,w_750,h_400,` : "";
      if (index % 3 === 0 && index !== 0) {
        groupIndex += 1;
        articleGroup[groupIndex] = [];
      }
      articleGroup[groupIndex].push(item);
    });
} catch (err) {
  console.error(err);
}
const carousel = ref();
const carouselNext = () => {
  carousel.value.next();
};
const carouselPrev = () => {
  carousel.value.prev();
};
const onOpen = url => {
  window.location.href = url;
};
const sceneGroup = [
  [
    {
      title: "企事业单位食堂配送",
      img: sceneShitangPeisong,
      desc: ["降低损耗", "高效管理食堂配送"],
      url: "/qsy/",
    },
    {
      title: "餐饮酒店配送",
      img: sceneJiudianPeisong,
      desc: ["降本增效", "一站式餐饮酒店管理解决方案"],
      url: '/p2/',
    },
    {
      title: "团餐团膳食堂承包供应链",
      img: sceneShitangChengbao,
      desc: ["颠覆传统管理模式", "让团膳食堂配送更高效可控"],
      url: '/tuanshan/',
    },
    // {
    //   title: '高校食堂供应链管理',
    //   img: sceneShitangGongyinglian,
    //   desc: ['有效规范供应商、档口行为', '保障食品安全'],
    // },
    {
      title: "学生营养餐中央厨房",
      img: sceneZhangyangChufang,
      desc: ["保障食品安全", "助力央厨企业降本增效"],
      url: '/xsyyc/',
    },
    {
      title: "肉类加工与分割",
      img: sceneRouleiJiagong,
      desc: ["精细化全流程管控", "成本可视可控"],
      url: '/roujg/',
    },
  ],
  [
    {
      title: "净菜加工",
      img: sceneJingcaiJiagong,
      desc: ["净菜加工流程标准化", "实现企业降本增效"],
      url: '/jcjg/',
    },
  ],
];
const kehuRenkeList = [
  {
    title: "浙江素尚农业有限公司",
    url: 'https://www.sdongpo.com/xueyuan/c-39819.html',
    logo: "",
    vedio: "https://ci-files.oss-cn-beijing.aliyuncs.com/sdpwebsite/sushang.mp4",
    desc: "“刚开始推荐使用软件的时候，我亲戚都反对的，都是一边做一边骂，等后来真正用了一个礼拜之后，真的方便很多。在蔬东坡夜间群里，不管什么时候，你发一个信息也好，拍个视频也好，马上就有人回复，我们之前想都不敢想，这行如果真的碰到问题没解决，那可是真的要歇菜了。”——杭州素尚董事长 李尚",
  },
  {
    title: "浙江绿而康农副产品有限公司",
    url: 'https://www.sdongpo.com/xueyuan/c-40090.html',
    logo: "",
    vedio: "https://ci-files.oss-cn-beijing.aliyuncs.com/sdpwebsite/lverkang.mp4",
    desc: "“我们跟蔬东坡合作是在14年，信息化肯定是这个行业的一个趋势，相当于你是一个节约人工费用，然后增加一些标准化的东西。时效性提高了以后，对企业和员工都是好事，智能化的软件配套都能应用起来的话，对于客户来说也是更放心一点。”——杭州绿而康总经理 张鹏",
  },
  {
    title: "北京利源百发商贸有限公司",
    url: 'https://www.sdongpo.com/xueyuan/c-40095.html',
    logo: "",
    vedio: "https://ci-files.oss-cn-beijing.aliyuncs.com/sdpwebsite/liyuan.mp4",
    desc: "“就是客户也要求，你要有一些小程序，能够下订单的，咱们蔬东坡就可以满足，手机端包括电脑端都能够实现，我们可能占了70%客户都在用（小程序下单），这个功能我们都觉得很便利”——北京利源百发总经理官勇军",
  },
  {
    title: "雄安央厨餐饮管理连锁有限公司",
    url: 'https://www.sdongpo.com/xueyuan/c-39995.html',
    logo: "",
    vedio: "https://ci-files.oss-cn-beijing.aliyuncs.com/sdpwebsite/xiongan.mp4",
    desc: "“和蔬东坡结缘以后软件的使用，很大程度地提升我们的管理水平，因为他对数据的统计收集，尤其对我们财务分析、市场监管、账目显示就更加清晰透明，因为你扫码进库、扫码出库，自动记录你花了多少钱是有统计的，你卖了多少钱也是有统计的，人工占多大比例也是有统计的。”——雄安央厨总经理乔栋",
  },
  {
    title: "长沙全尔美农产品贸易有限公司",
    url: 'https://www.sdongpo.com/xueyuan/c-40104.html',
    logo: "",
    vedio: "https://ci-files.oss-cn-beijing.aliyuncs.com/sdpwebsite/quanermei.mp4",
    desc: "“我们了解到蔬东坡这个系统，我就觉得这东西真就是为我们公司量身定做的，如果说我们像之前的那种手写，送完一个月之后，我压根就不知道，我这一个月送了多少土豆出去，有了这个系统之后，我甚至于都知道我早前几年，我们哪一个月的营收是多少，我觉得这个是对我最大的一个帮助。”——长沙全尔美总经理胡坤龙",
  },
];
const handleCarouselChange = (nowIndex, originIndex) => {
  const carouselItem = document.querySelectorAll(".kehurenke-container .el-carousel__item")[originIndex];
  if (carouselItem) {
    const videoItem = carouselItem.querySelector("video");
    // 暂停上一个视频播放
    if (videoItem && !videoItem.paused) videoItem.pause();
  }
  const nowCarouselItem = document.querySelectorAll(".kehurenke-container .el-carousel__item")[nowIndex];
  if (nowCarouselItem) {
    const videoItem = nowCarouselItem.querySelector("video");
    // 自动播放当前视频
    videoItem && videoItem.play();
  }
};
</script>
<style lang="postcss" scoped>
@import url(~/assets/style/tool.css);
.home {
  width: 100%;
  overflow: hidden;
}

.bg-gray {
  background: #f9fbff;
}

.header {
  height: 737px;
  background: url(../assets/index/header-bg.png) no-repeat;
  background-size: cover;
  position: relative;
  color: #fff;
  overflow-x: hidden;
  letter-spacing: 1px;
  background-size: 100%;
}

.header-container {
  position: absolute;
  top: 0;
  left: 50%;
  margin-left: -960px;
  width: 1916px;
  height: 737px;
}

.title {
  position: absolute;
  display: block;
  width: 750px;
  /* top: 295px; */
  bottom: 420px;
  left: 315px;
  font-size: 54px;
  line-height: 56px;
  font-weight: bold;
  h1 {
    display: inline-block;
  }
}

.title-number {
  font-size: 110px;
}

.sub-title {
  position: absolute;
  font-size: 32px;
  left: 315px;
  /* top: 465px; */
  bottom: 332px;
}
.padleft {
  padding-left: 60px;
}
.form-box {
  position: absolute;
  width: 480px;
  height: 306px;
  left: 1110px;
  top: 224px;
  background: rgba(35, 90, 226, 0.7);
  border-radius: 10px;
  padding-top: 35px;
}

.form-title {
  font-size: 26px;
  margin-bottom: 30px;
  text-align: center;
}

.form-item {
  display: flex;
  flex-direction: row;

  &:nth-of-type(1) {
    margin-bottom: 30px;
  }
}

.label {
  font-size: 16px;
  padding-bottom: 14px;
  margin-right: 38px;
  border-bottom: 1px solid #fff;
}

.field {
  width: 274px;
  background: transparent;
  border-bottom: 1px solid #fff;
}

.field input {
  height: 100%;
  width: 100%;
  padding-bottom: 14px;
  color: #fff;
  background: transparent;
  outline: none;
}

.field input::placeholder {
  background: transparent;
  font-size: 16px;
  color: rgba(255, 255, 255, 0.5);
}

.submit-btn {
  width: 159px;
  height: 41px;
  line-height: 41px;
  text-align: center;
  display: block;
  margin: 30px auto 0;
  background: #ff9e35;
  border-radius: 5px;
  color: #fff;
  cursor: pointer;
}

@keyframes liucheng-move {
  from {
    opacity: 0;
    transform: translateX(-200%);
  }

  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes liucheng-move-right {
  from {
    opacity: 0;
    transform: translateX(200%);
  }

  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes liucheng-move-top {
  from {
    opacity: 0;
    transform: translateY(-200%);
  }

  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.liucheng {
  text-align: center;

  &--show {
    .tip {
      opacity: 1;
    }

    .top-left {
      animation: liucheng-move 1s;
      animation-delay: 500ms;
    }

    .top-right {
      animation: liucheng-move-right 1s;
      animation-delay: 1s;
    }

    .left-middle {
      animation: liucheng-move 1s;
      animation-delay: 2s;
    }

    .center {
      animation: liucheng-move-top 1s;
      animation-delay: 3s;
    }

    .right-middle {
      animation: liucheng-move-right 1s;
      animation-delay: 4s;
    }

    .bottom-left {
      animation: liucheng-move 1s;
      animation-delay: 5s;
    }

    .bottom-right {
      animation: liucheng-move-right 1s;
      animation-delay: 6s;
    }
  }

  &-main {
    position: relative;
    width: 1232px;
    height: 693px;
    margin: 0 auto;
    background: url(../assets/index/yewuliucheng-bg.png) no-repeat top/cover;
    background-position-x: 250px;
    background-position-y: 126px;
    background-size: 600px;

    img {
      position: absolute;
      cursor: pointer;
      transition: all 300ms;

      &:hover {
        transform: scale(1.1);
      }
    }
  }

  &-item:hover {
    .tip {
      transform: translate(-10px, -10px);
    }
  }

  &-fenjian:hover,
  &-suyuan:hover {
    .tip {
      transform: translate(10px, 10px);
    }
  }

  &-caigou {
    img {
      position: absolute;
      left: 206px;
      top: 86px;
      width: 442px;
    }
  }

  &-cangku {
    img {
      left: 600px;
      top: 78px;
      width: 269px;
    }
  }

  &-xiadan {
    img {
      top: 256px;
      left: 70px;
      width: 246px;
    }
  }

  &-fenjian {
    img {
      width: 400px;
      top: 166px;
      left: 686px;
    }
  }

  &-peisong {
    img {
      width: 318px;
      top: 310px;
      left: 496px;
    }
  }

  &-shuju {
    img {
      width: 301px;
      top: 438px;
      left: 290px;
    }
  }

  &-suyuan {
    img {
      width: 484px;
      top: 412px;
      left: 624px;
    }
  }

  .tip {
    position: absolute;
    white-space: nowrap;
    padding: 10px 15px;
    cursor: pointer;
    transform: translate(0, 0);
    transition: all 300ms;

    background: #858e9e;
    border-radius: 5px;
    color: #fff;
    text-align: left;

    &-title {
      font-size: 16px;
      margin-bottom: 5px;
    }

    &-desc {
      font-size: 14px;
      line-height: 22px;
    }

    &.top-left {
      top: 100px;
      left: 40px;
      height: 90px;
    }

    &.top-right {
      top: 24px;
      right: 96px;
      /* width: 350px; */
    }

    &.left-middle {
      top: 340px;
      left: -170px;
    }

    &.center {
      top: 210px;
      left: 570px;
      width: 170px;
    }

    &.right-middle {
      top: 218px;
      left: 1112px;
      /* width: 220px; */
    }

    &.bottom-left {
      top: 520px;
      left: 130px;
      /* width: 205px; */
    }

    &.bottom-right {
      top: 570px;
      left: 1100px;
      /* width: 210px; */
    }

    &::after {
      content: "";
      display: block;
      position: absolute;
    }

    &.arrow-left::after,
    &.arrow-right::after {
      top: 50%;
      transform: translateY(-50%);
      border-top: 10px solid transparent;
      border-bottom: 10px solid transparent;
    }

    &.arrow-right::after {
      right: -24px;
      border-right: 16px solid transparent;
      border-left: 16px solid #858e9e;
    }

    &.arrow-left::after {
      left: -24px;

      border-right: 16px solid #858e9e;
      border-left: 16px solid transparent;
    }

    &.arrow-down::after {
      bottom: -24px;
      left: 50%;
      transform: translateX(-50%);
      border-left: 10px solid transparent;
      border-right: 10px solid transparent;
      border-bottom: 16px solid transparent;
      border-top: 16px solid #858e9e;
    }
  }

  &-btn {
    width: 200px;
    text-align: center;
    font-size: 20px;
  }
}

.feature-tab {
  position: relative;
  width: 1070px;
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  margin: 0 auto;

  &::after {
    content: "";
    display: block;
    width: 1200px;
    position: absolute;
    bottom: -3px;
    left: 50%;
    transform: translateX(-50%);
    border-bottom: 3px solid #2977fe;
  }

  &-container {
    padding-top: 70px;
    margin-top: 70px;
  }

  &-title {
    position: relative;
    display: flex;
    justify-content: center;
    padding-bottom: 20px;
    font-size: 28px;
  }

  &__item {
    width: 150px;
    height: 96px;
    font-size: 16px;
    text-align: center;
    color: #3365f6;
    padding: 15px 10px;
    position: relative;
    cursor: pointer;
    margin-bottom: 32px;

    &.active {
      /* background: url(../assets/index/tab-bg.png) no-repeat center; */
      /* background-size: 150px; */
      border: 1px dashed #3365f6;
      background: rgba(228, 238, 255, 0.8);
      border-radius: 5px;

      &::after {
        bottom: -43px;
      }
    }

    &::after {
      content: "";
      display: block;
      position: absolute;
      bottom: -42px;
      left: 50%;
      transform: translateX(-50%);
      width: 16px;
      height: 16px;
      border-radius: 50%;
      z-index: 3;
      background: url(../assets/index/icon-radio.png) no-repeat center;
      background-size: 14px;
    }
  }

  &__desc {
    margin-top: 10px;
    height: 23px;
    line-height: 23px;

    img {
      width: 23px;
      height: 23px;
      margin-right: 10px;
    }

    display: flex;
    flex-direction: row;
    align-content: center;
    justify-content: center;
  }

  &__content {
    padding: 70px 0;
  }
}

.scene {
  &-container {
    width: 1560px;
    margin: 0 auto;
    padding-top: 70px;

    /deep/ {
      .el-carousel__arrow--left {
        background: url(../assets/index/carousel-arrow-left-white.png) no-repeat center;
        background-size: 23px;

        .el-icon {
          display: none;
        }
      }

      .el-carousel__arrow--right {
        background: url(../assets/index/carousel-arrow-right-white.png) no-repeat center;
        background-size: 23px;

        .el-icon {
          display: none;
        }
      }
    }
  }

  &-title {
    font-size: 28px;
    margin: 0 auto;
    text-align: center;
  }

  &-sub-title {
    font-size: 16px;
    color: #666666;
    margin: 10px auto 40px;
    text-align: center;
  }

  &-mask {
    background: url(../assets/index/scene-bg.png) no-repeat center;
    background-size: cover;
  }

  &-list {
    display: flex;
    flex-direction: row;
    justify-content: left;
  }

  &-item {
    height: 435px;
    width: 313px;
    position: relative;
    overflow: hidden;
    color: #fff;
    cursor: pointer;
    text-align: left;

    img {
      width: 100%;
      height: 100%;
    }

    &__title {
      font-size: 22px;
      padding: 0 25px;

      &-show {
        position: absolute;
        left: 20px;
        bottom: 50px;
      }
    }

    &__desc {
      font-size: 18px;
      padding: 0 25px;

      p {
        margin-bottom: 6px;
      }
    }
  }

  &-mask {
    position: absolute;
    bottom: -435px;
    left: 0;
    width: 100%;
    height: 435px;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    padding-top: 35px;
    padding-bottom: 60px;
    transition: all 300ms;
  }

  &-item:hover &-mask {
    bottom: 0;
  }
}

.gonglue {
  &-title {
    color: #333;
    font-size: 28px;
    text-align: center;
  }

  &-sub-title {
    font-size: 16px;
    color: #666;
    margin: 10px 0 47px;
    text-align: center;
  }

  &-container {
    padding: 70px 0 70px;
  }

  &-content {
    width: 1296px;
    margin: 0 auto;
  }

  &-list {
    display: flex;
    flex-direction: row;
    justify-content: space-between;
  }

  &-item {
    width: 300px;
    height: 350px;
    position: relative;
    text-align: center;
    overflow: hidden;
    background-color: #fff;

    &-btn {
      cursor: pointer;
      position: absolute;
      left: 0;
      bottom: -40px;
      height: 40px;
      line-height: 40px;
      font-size: 16px;
      width: 100%;
      text-align: center;
      background: #2977fe;
      color: #fff;
      border-radius: 0;
      transition: all 300ms;
    }

    &:hover &-btn {
      bottom: 0;
    }

    &-img {
      position: relative;
      overflow: hidden;
      img {
        width: 100%;
        height: 200px;
      }
      &-link {
        cursor: pointer;
      }
    }

    &-title {
      color: #000;
      font-size: 20px;
      margin: 20px 0 10px;
    }

    &-sub-title {
      font-size: 16px;
      color: #333;
      margin-bottom: 10px;
    }

    &-desc {
      font-size: 12px;
      color: #999;
    }
  }
}
.kehurenke {
  &-container {
    width: 1314px;
    margin: 0 auto;
    padding: 70px 0 70px;

    /deep/ {
      .el-carousel__arrow--left {
        background: url(../assets/index/carousel-arrow-left.png) no-repeat center/contain;
        background-size: 23px;

        .el-icon {
          display: none;
        }
      }

      .el-carousel__arrow--right {
        background: url(../assets/index/carousel-arrow-right.png) no-repeat center/contain;
        background-size: 23px;

        .el-icon {
          display: none;
        }
      }
    }
  }

  &-title {
    font-size: 28px;
    font-family: PingFang SC-Medium, PingFang SC;
    color: #333333;
    margin: 0 auto;
    text-align: center;
  }

  &-sub-title {
    font-size: 16px;
    color: #666;
    margin: 10px 0 40px;
    text-align: center;
  }

  &-item {
    background-color: #f2f6fb;
    padding: 37px 56px 0 104px;
    display: flex;
    flex-direction: row;
    align-content: center;
    height: 375px;

    &-video {
      width: 471px;
      height: 275px;
    }

    &-title {
      display: flex;
      flex-direction: row;
      font-size: 24px;
      color: #333;
      align-content: center;
      padding-left: 51px;
      line-height: 64px;
    }

    &-logo {
      width: 63px;
      height: 63px;
      margin-right: 9px;
    }

    &-desc {
      font-size: 14px;
      color: #666;
      padding: 26px 86px 0 51px;
      font-size: 14px;
      line-height: 30px;
      text-align: left;
    }
  }
}

.kehu-list {
  padding: 70px 0 70px;

  &-main {
    width: 1600px;
    margin: 0 auto;
  }

  &-title {
    font-size: 28px;
    color: #333333;
    margin: 0 auto 37px;
    text-align: center;
  }

  &-sub-title {
    font-size: 16px;
    color: #666;
    margin: 10px auto 40px;
    text-align: center;
    display: block;
  }

  &-content {
    display: flex;
    flex-direction: row;
    flex-wrap: wrap;
    justify-content: space-between;
  }
}

.article {
  &-title {
    font-size: 28px;
    font-family: PingFang SC-Medium, PingFang SC;
    color: #333333;
    margin: 0 auto 40px;
    text-align: center;
  }

  &-container {
    width: 1200px;
    margin: 0 auto 60px;
    position: relative;
    padding-top: 95px;

    /deep/ {
      --el-carousel-indicator-width: 10px;
      --el-carousel-indicator-height: 10px;
      --el-carousel-indicator-out-color: #d9d9d9;
      --el-carousel-indicator-padding-vertical: 0;
      --el-carousel-indicator-padding-horizontal: 10px;

      .el-carousel {
        text-align: center;
      }

      .el-carousel__arrow {
        top: unset;
        width: 26px;
        height: 16px;
        bottom: -65px;

        .el-icon {
          display: none;
        }

        &--left {
          left: 470px;
          background: url(../assets/index/arrow-left.png) no-repeat center;
        }

        &--right {
          background: url(../assets/index/arrow-right.png) no-repeat center;
          right: 470px;
        }
      }

      .el-carousel__indicators--outside {
        display: inline-block;
        margin: 44px auto 0;
        line-height: 0;

        .is-active {
          button {
            background-color: #2977fe;
            width: 25px;
            border-radius: 16px;
          }
        }

        button {
          border-radius: 50%;
          opacity: 1;
        }
      }
    }
    .more {
      position: absolute;
      color: #333;
      font-size: 16px;
      right: 3px;
      cursor: pointer;
    }

    .indicator {
      z-index: -1;
      width: 100%;
      position: absolute;
      height: 20px;
      bottom: 0px;
      left: 0;
    }
  }

  &-list {
    margin: 0 auto;
    display: flex;
    flex-direction: row;
    justify-content: center;
  }

  &-item {
    width: 357px;
    height: 300px;
    cursor: pointer;

    &:nth-child(2) {
      margin: 0 63px;
    }

    img {
      width: 357px;
      height: 200px;
    }
  }

  &-content-title {
    background: #2872f1;
    opacity: 0.95;
    font-size: 16px;
    color: #fff;
    display: block;
    padding: 16px 12px;
    span {
      display: block;
      word-break: keep-all;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
      line-height: 18px;
    }
  }
}

.newMessage {
  &-title {
    font-size: 28px;
    font-family: PingFang SC-Medium, PingFang SC;
    color: #333333;
    margin: 0 auto 40px;
    text-align: center;
  }

  &-container {
    position: relative;
    display: flex;
    flex-direction: column;
    justify-content: center;
    padding-bottom: 80px;
    padding-top: 30px;
  }
  &-info {
    width: 100%;
    display: flex;
    justify-content: center;
  }
  &-info-list {
    width: 62.5vw;
    position: relative;
  }

  &-more {
    background-color:#fff;
    width: 62.5vw;
    height: 40px;
    position: absolute;
    text-align: right;
    font-size: 16px;
  }
  &-more-text {
    color: #333;
    position: absolute;
    font-size: 16px;
    right: 20px;
    bottom: 20px;
  }
}

/deep/ {
  --el-carousel-indicator-width: 10px;
  --el-carousel-indicator-height: 10px;
  --el-carousel-indicator-out-color: #d9d9d9;
  --el-carousel-indicator-padding-vertical: 0;
  --el-carousel-indicator-padding-horizontal: 10px;
}
/deep/ .el-carousel--horizontal {
  overflow-y: hidden;
}
</style>
