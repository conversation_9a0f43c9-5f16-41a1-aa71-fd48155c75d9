<template>
  <div class="tool">
    <!-- <div class="left__container common__box">
      <div class="resource__items" @click="handleShowModal(ModalType.datum)" backTop>
          <img src="~/assets/common/tool_icon5.png" alt="资料下载" width="72" height="72" />
      </div>
      <div class="tool__items" @click="handleShowModal(ModalType.datum)">
        <img src="~/assets/common/tool_icon6.png" alt="运营大全" />
        <div>
          <p>生鲜企业</p>
          <p>运营大全</p>
        </div>
      </div>
      <div class="tool__items" @click="handleShowModal(ModalType.datum)">
        <img src="~/assets/common/tool_icon7.png" alt="运营手册" width="32" height="32" />
        <div>
          <p>中央厨房</p>
          <p> 运营手册</p>
        </div>
      </div>
      <div class="tool__items" @click="handleShowModal">
        <img src="~/assets/common/tool_icon8.png" alt="行业资料" width="32" height="32" />
        <div>
          <p>肉类分割</p>
          <p>行业资料</p>
        </div>
      </div>
    </div> -->
    <div class="right__container common__box">
      <div class="service__items" backTop>
        <a target="_blank" href="https://p.qiao.baidu.com/cps/chat?siteId=6424386&amp;userId=9973984">
          <img src="~/assets/common/tool_icon1.png" alt="专属客服" width="72" height="72" />
          <p>专属客服</p>
        </a>
      </div>
      <div class="tool__list">
        <div class="tool__items" @click="handleShowModal(ModalType.common)">
          <img src="~/assets/common/tool_icon2.png" alt="免费试用" />
          <p>免费试用</p>
        </div>
        <div class="tool__items backTop" backTop @click="scrollTop">
          <img src="~/assets/common/tool_icon4.png" alt="返回顶部" width="32" height="32" />
          <p>返回顶部</p>
        </div>
        <div class="tool__items" @click="handleShowModal(ModalType.price)">
          <img src="~/assets/common/tool_icon3.png" alt="方案报价" width="32" height="32" />
          <p>方案报价</p>
        </div>
      </div>
      <div class="resource__items" @click="handleShowModal(ModalType.datum)">
          <img src="~/assets/common/tool_icon5.png" alt="资料下载" width="72" height="72" />
      </div>
      <!--       <div class="tool__items" @click="handleShowModal">
        <img class="tool-icon-margin" src="~/assets/common/bai.png" alt="获取白皮书" width="21" height="18" />
        <p>获取白皮书</p>
      </div>
      <div class="tool__items">
        <img
          src="https://website-image.sdongpo.com/website/index/weixin.png?ef45b456c6"
          alt="扫码关注"
          width="28"
          height="23"
        />
        <p>扫码关注</p>
        <div class="tool__items--modal qrcode">
          <img
            src="https://website-image.sdongpo.com/website/index/onlineService.png?4a4be84232"
            alt="扫码联系客服"
            width="200"
            height="200"
          />
          <p>扫码联系客服</p>
        </div>
      </div>
      <div class="tool__items">
        <img
          src="https://website-image.sdongpo.com/website/index/tel.png?324595b75a"
          alt="咨询热线"
          width="22"
          height="24"
        />
        <p>咨询热线</p>
        <div class="tool__items--modal tel">
          <p>售前咨询</p>
          <p>************</p>
        </div>
      </div>
      <a
        target="_blank"
        href="https://p.qiao.baidu.com/cps/chat?siteId=6424386&amp;userId=9973984"
        class="tool__items"
        class="tool-link-block"
      >
        <img
          src="https://website-image.sdongpo.com/website/index/online.png?99ca1f8a07"
          alt="在线客服"
          width="24"
          height="22"
        />
        <p>在线客服</p>
      </a>
      <div class="tool__items backTop" @click="scrollTop">
        <img
          class="tool-icon-margin"
          src="https://website-image.sdongpo.com/website/index/return__top.png?303a3fd370"
          alt="返回顶部"
          width="21"
          height="18"
        />
        <p>返回顶部</p>
      </div> -->
    </div>
  </div>
</template>
<script setup>
import { ModalType } from '~/composables';

const handleShowModal = openModal;
const scrollTop = () => {
  document.querySelector('#__nuxt').scrollTop = 0;
};
</script>
<style lang="postcss" scoped>
@import url(~/assets/style/tool.css);
.tool * {
  -moz-box-sizing: content-box;
  box-sizing: content-box;
  font-family: PingFangSC-Regular;
}
.tool {
  .common__box {
    position: fixed;
    top: 237px;
    right: 86px;
    z-index: 999;
    cursor: pointer;
  }

  .right__container {
    display: flex;
    flex-direction: column;
    align-items: center;
    .service__items {
      position: relative;
      height: 85px;
      width: 70px;
      padding-bottom: 10px;
      animation-name: scaleDraw;
      animation-timing-function: ease-in-out;
      animation-iteration-count: infinite;
      animation-duration: 3s;
      p {
        width: 100%;
        position: absolute;
        top: 60px;
        font-weight: 400;
        font-size: 12px;
        text-align: center;
        white-space: nowrap;
        box-shadow: 4px 4px 4px 0px rgba(0,0,0,0.1);
        color: #2977fe;
        border-radius: 5px 5px 5px 5px;
        background-color: #fff;
        opacity: 1;
      }
      img {
        display: block;
        margin: 0 auto;
        width: 72px;
        height: 72px;
      }
    }
    .tool__list {
      background: #FFFFFF;
      box-shadow: 2px 2px 4px 0px rgba(0,0,0,0.1);
      border-radius: 10px;
      border: 1px solid transparent;
      overflow: hidden;
      width: 70px;
      display: flex;
      flex-direction: column;
      align-items: center;
    }
    .tool__items {
      position: relative;
      min-height: 52px;
      width: 40px;
      padding-top: 12px;
      padding-bottom: 12px;
      text-align: center;
      background: #fff;
      display: flex;
      flex-direction: column;
      justify-content: space-between;
      align-items: center;
      border-bottom: 1px solid #D9D9D9;
      &:last-child {
        border-bottom: none;
      }
    }
    .tool__items > img {
      display: block;
      margin: 0 auto;
      width: 32px;
      height: 32px;
    }
    .tool__items > p {
      font-size: 12px;
      white-space: nowrap;
      line-height: 14px;
      font-family: PingFangSC-Light;
      color: #2977fe;
    }
    
    .resource__items { 
      margin-top: 8px;
      display: flex;
      justify-content: center;
      cursor: pointer;
      img {
        width: 72px;
      }
    }
    @keyframes scaleDraw {
      0%{
        transform: scale(1);
      }
      25%{
        transform: scale(1.1);
      }
      50%{
        transform: scale(1);
      }
      75%{
        transform: scale(1.1);
      }
    }
  }
  
}
</style>
