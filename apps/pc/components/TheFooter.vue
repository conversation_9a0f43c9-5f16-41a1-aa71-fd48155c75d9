<!--
 * @Author: ddcoder <PERSON><PERSON><PERSON><PERSON>@movee.cn
 * @Date: 2023-03-17 13:53:12
 * @LastEditors: fc <EMAIL>
 * @LastEditTime: 2023-06-09 14:55:09
-->
<script setup></script>
<template>
  <div class="wrap">
    <div class="footer-container">
      <div class="main">
        <div class="logo-part">
          <img class="logo" src="../assets/common/logo-white.png" alt="蔬东坡" />
          <div class="shouqian">
            <p>售前咨询</p>
            <p>400-075-1863</p>
          </div>
          <div class="shouqian">
            <p>售后咨询</p>
            <p>400-688-8750</p>
          </div>
          <div class="wechat">
            <img src="../assets/common/wechat-qrcode.png" alt="蔬东坡" />
            <p>蔬东坡微信公众号</p>
          </div>
        </div>
        <div class="inner-link">
          <div class="inner-link-group" v-for="(group, index) in innerLinkGroup" :key="index">
            <div class="inner-link-group-title">{{ group.title }}</div>
            <a class="inner-link-item" :href="item.link" v-for="(item, itemIndex) in group.children" :key="itemIndex">
              {{ item.title }}
            </a>
          </div>
        </div>
      </div>
      <div class="copyright">
        <p>北京木屋时代科技有限公司为企业提供SaaS + ERP 生鲜供应链系统、社区团购系统.</p>
        <p>
          北京木屋时代科技有限公司(© 2014-{{new Date().getFullYear()}}
          <a href="https://beian.miit.gov.cn/" target="_blank">京ICP备14021337号-2</a>
          )
        </p>
        <div class="beian-container">
          <a
            target="_blank"
            href="http://www.beian.gov.cn/portal/registerSystemInfo?recordcode=11010502052243"
            class="beian-link"
          >
            <img src="../assets/common/beian.png" class="beian-icon" />
            <p class="beian-text">
              京公网安备 11010502052243号
            </p>
          </a>
        </div>
      </div>
      <div class="friend-link">
        <div class="friend-link-list">
          <a :href="item.link" target="_blank" v-for="(item, index) in friendLinkList" :key="index">{{ item.title }}</a>
        </div>
      </div>
    </div>
  </div>
</template>
<script setup>
import { reactive } from "vue";
onMounted(() => {
  changeLink(innerLinkGroup);
});
const innerLinkGroup = reactive([
  {
    title: "产品",
    children: [
      {
        title: "生鲜配送系统",
        link: "/shicai/",
      },
      {
        title: "中央厨房系统",
        link: "/processing/",
      },
      // {
      //   title: '福得云管理系统',
      //   link: '/yzc/',
      // },
      {
        title: "社区团购系统",
        link: "/groupbuy/",
        out: true,
      },
      {
        title: "生态云平台",
        link: "/products/business/",
        out: true,
      },
      {
        title: "智能硬件",
        link: "/products/hardware/",
        out: true,
      },
    ],
  },
  {
    title: "解决方案",
    children: [
      {
        title: "企事业单位食堂配送",
        link: "/qsy/",
      },
      {
        title: "餐饮酒店配送",
        link: "/p2/",
      },
      {
        title: "团餐团膳食堂承包供应链",
        link: "/tuanshan/",
      },
      {
        title: "高校食堂食材供应管理",
        link: "/gxst/",
      },
      {
        title: "学生营养餐",
        link: "/xsyyc/",
      },
      {
        title: "肉类加工与分割",
        link: "/roujg/",
      },
      {
        title: "净菜加工",
        link: "/jcjg/",
      },
      {
        title: "预制菜",
        link: "/yzc/",
      },
    ],
  },
  {
    title: "客户案例",
    children: [
      {
        title: "客户案例",
        link: "/movee/",
        out: true,
      },
    ],
  },
  {
    title: "学习中心",
    children: [
      {
        title: "生鲜课堂",
        link: "/sxkt/",
        out: true,
      },
      {
        title: "央厨课堂",
        link: "/yckt/",
        out: true,
      },
      {
        title: "资料下载",
        link: "/zlxz/",
        out: true,
      },
      {
        title: "行业会议",
        link: "/hymt/",
      },
      {
        title: "专家咨询",
        link: "/epsf/",
      },
      {
        title: "新闻报道",
        link: "/xueyuan/",
      },
    ],
  },
  {
    title: "服务与支持",
    children: [
      {
        title: "服务保障",
        link: "/movee/service/",
        out: true,
      },
      {
        title: "帮助中心",
        link: "https://helpcenter.sdongpo.com",
      },
    ],
  },
  {
    title: "关于蔬东坡",
    children: [
      {
        title: "企业介绍",
        link: "/about/",
        out: true,
      },
      {
        title: "联系我们",
        link: "/about/contact/#contact",
        out: true,
      },
    ],
  },
]);
const friendLinkList = [
  {
    link: "https://www.crm.cc/",
    title: "免费CRM",
  },
  {
    link: "https://www.sdwanyue.com/",
    title: "教育系统",
  },
  {
    link: "http://www.yunbaokj.com/",
    title: "直播平台源码",
  },
  {
    link: "https://www.chedianzhang.com/",
    title: "汽车维修管理软件",
  },

  {
    link: "https://www.nakesoft.com/",
    title: "收银系统",
  },
  {
    link: "https://www.ikscrm.com/",
    title: "企微SCRM系统",
  },
  {
    link: "http://www.shijiance.com/",
    title: "农药残留检测仪",
  },
  {
    link: "https://www.8jcrm.com/",
    title: "企业CRM",
  },
  {
    link: "https://www.shopxx.net/",
    title: "电商系统",
  },
  {
    title: "绩效管理软件",
    link: "https://www.pekhr.com",
  },
  {
    title: "画册设计",
    link: "http://www.szhcgg.com",
  },
  {
    title: "识微科技",
    link: "https://www.civiw.com/",
  },
  {
    title: "校园一卡通系统",
    link: "http://www.xxykt.cn",
  },
  {
    title: "企业财务服务",
    link: "https://www.caika.net",
  },
  {
    title: "研磨仪",
    link: "https://www.tissuelyser.com",
  },
  {
    title: "质量流量控制器",
    link: "http://www.crzdh.cn",
  },
  {
    title: "医学教育网",
    link: "https://www.yixuemao.com",
  },
  {
    title: "智能电表",
    link: "https://www.ymsino.com",
  },
  {
    title: "CRM客户关系管理系统",
    link: "https://www.frensworkz.com",
  },
  {
    title: "铸铁井盖厂家",
    link: "http://www.sccdjgcj.com",
  },
  {
    title: "三元乙丙橡胶垫",
    link: "http://www.jingdong.cn",
  },
  {
    title: "招标网",
    link: "https://www.zhaobiao.cn",
  },
  {
    title: "金钥匙跨境",
    link: "https://www.51969.com",
  },
  {
    title: "智慧工地",
    link: "http://www.zxwis.cn",
  },
  {
    title: "收银软件",
    link: "https://www.td365.com.cn",
  },
  {
    title: "上海律师事务所",
    link: "https://www.hllawyers.com",
  },
];
</script>
<style scoped lang="postcss">
@import url(~/assets/style/footer.css);
.wrap {
  background: #232629;
}

.footer-container {
  width: 1400px;
  margin: 0 auto;
  color: #fff;
  padding: 70px 0 20px;

  .main {
    display: flex;
    flex-direction: row;
    justify-content: space-between;
  }

  .logo-part {
    font-size: 20px;

    p {
      margin-bottom: 5px;
    }

    .logo {
      width: 215px;
    }

    .shouqian {
      margin: 30px 0;
    }

    .shouhou {
      margin-bottom: 30p;
    }

    .wechat {
      font-size: 14px;

      img {
        width: 120px;
        margin-bottom: 10px;
      }
    }
  }

  .inner-link {
    display: flex;
    flex-direction: row;

    &-group {
      margin-left: 50px;

      &-title {
        display: inline-block;
        font-size: 20px;
        padding: 0 16px 19px 0;
        margin-bottom: 20px;
        border-bottom: 1px solid rgba(255, 255, 255, 0.3);
      }
    }

    &-item {
      display: block;
      font-size: 16px;
      margin-bottom: 15px;
      opacity: 0.8;

      &:hover {
        opacity: 1;
      }
    }
  }

  .copyright {
    margin-top: 52px;
    margin-bottom: 26px;
    text-align: center;
    font-size: 12px;
    /* line-height: 14px; */
    opacity: 0.8;

    p {
      margin: 0;
    }
  }

  .friend-link {
    position: relative;
    opacity: 0.8;
    font-size: 12px;

    &::before {
      content: "";
      display: block;
      height: 1px;
      width: 100%;
      background-color: #fff;
      position: absolute;
      top: 0;
      left: 0;
    }

    &::after {
      content: "[友情链接]-(申请qq1975483517)";
      display: block;
      position: absolute;
      top: -16px;
      left: 50%;
      transform: translateX(-50%);
      /* width: 209px; */
      height: 32px;
      line-height: 32px;
      /* text-align: center; */
      padding: 0 16px;
      font-size: 12px;
      /* border: 1px dashed rgba(255, 255, 255, 0.8); */
      background-color: #232629;
    }

    &-list {
      padding-top: 30px;

      a {
        display: inline-block;
        margin: 0 20px 26px;

        &:hover {
          opacity: 1;
        }
      }
    }
  }
}
</style>
