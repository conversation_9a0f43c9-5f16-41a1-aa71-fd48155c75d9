/* 专家咨询页面样式 */
.epsf {
  color: #333333;
}

.epsf .news_header {
  background: url(~/assets/xueyuan/common_bg.png) no-repeat center center;
  background-size: 100% 100%;
}

.epsf .news_header__desc {
  font-size: 28px;
}

.epsf .w {
  width: 850px;
  margin: 0 auto;
}

.epsf .g-block {
  background-color: #f9fbff;
  padding-top: 70px;
  overflow: hidden;
}

.epsf .w-block {
  background-color: #FFFFFF;
  padding-top: 50px;
  overflow: hidden;
}

.epsf .title2 {
  font-size: 24px;
  text-align: center;
  font-weight: 500;
}

.epsf .mb50 {
  margin-bottom: 50px;
}

.epsf .mb30 {
  margin-bottom: 30px;
}

.epsf .txt {
  color: #666666;
  line-height: 16px;
}

.epsf .person-list {
  margin: 50px auto 70px auto;
  display: flex;
  flex-wrap: wrap;
  gap: 30px 60px;
}

.epsf .person-list .person {
  flex-basis: 120px;
  flex-shrink: 0;
  vertical-align: top;
}

.epsf .person-list .person img {
  width: 120px;
  height: 120px;
  border-radius: 50%;
  border: 1px solid #2977FE;
}

.epsf .person-list .person .person-name {
  color: #2977FE;
  font-size: 16px;
  margin-top: 5px;
  text-align: center;
}

.epsf .person-list .person .person-desc {
  color: #999999;
  font-size: 12px;
  text-align: center;
}

.epsf .icon-list {
  margin-top: 50px;
  display: flex;
  flex-wrap: wrap;
  gap: 30px 40px;
}

.epsf .icon-list .icon {
  flex-basis: 70px;
  flex-shrink: 0;
}

.epsf .icon-list .icon img {
  width: 70px;
  border-radius: 50%;
}

.epsf .icon-list .icon p {
  font-size: 14px;
  color: #666666;
  text-align: center;
}

.epsf .icon-list .icon .txt1 {
  margin-top: 7px;
}

.epsf .desc-box {
  display: flex;
  margin-top: 20px;
  margin-bottom: 70px;
}

.epsf .desc-box img {
  width: 140px;
  height: 140px;
}

.epsf .desc-box .desc-txt {
  display: flex;
  align-items: center;
  font-size: 14px;
  margin-left: 24px;
  text-indent: 2em;
}

.epsf .ellipsis {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
