/* 资料下载页面样式 */
.down_box {
  width: 1000px;
  display: flex;
  flex-wrap: wrap;
  margin: auto;
  align-content: flex-start;
}

.article {
  margin: 10px 15px 20px 15px;
  border-radius: 3px 3px 3px 3px;
  box-shadow: 1px 1px 1px 1px rgba(28, 102, 231, 0.1);
}

.down_title {
  height: 195px;
  width: 300px;
  cursor: pointer;
  position: relative;
  background: #2977fe;
}

.down_title .img_title {
  position: absolute;
  top: 50%;
  left: 53%;
  width: 100px;
  text-align: center;
  transform: translate(-50%, -50%);
  font-size: 12px;
  color: #333333;
}

.down_title img {
  width: 181px;
  height: 162px;
  margin: 0 auto;
  padding-top: 23px;
}

.downbox {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 12px;
}

.down_click {
  background: #EAF1FF;
  color: #2977FE;
  width: 80px;
  padding: 5px 0;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 3px 3px 3px 3px;
  cursor: pointer;
  float: right;
  font-size: 12px;
}

.txt-box {
  padding: 30px 13px 25px 18px;
  width: 300px;
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
}

.txt-box .title {
  font: bolder 18px PingFang SC-Medium, PingFang SC;
}

.hymt {
  color: #333333;
}

.hymt .news_header {
  background: url(~/assets/xueyuan/common_bg.png) no-repeat center center;
  background-size: 100% 100%;
}

.hymt .news_header__desc {
  font-size: 28px;
}

.hymt .g-block {
  background-color: #f9fbff;
  padding-top: 50px;
  padding-bottom: 50px;
}

.hymt .w {
  width: 960px;
  margin: 0 auto;
}

.hymt .w1 {
  width: 1100px;
  margin: 0 auto;
}

.hymt .title2 {
  font-size: 24px;
  font-weight: 500;
  margin-bottom: 10px;
  text-align: center;
}

.hymt .desc2 {
  text-align: center;
  font-size: 16px;
  color: #666666;
}

/* Modal styles */
.modal__mask {
  position: fixed;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.7);
  z-index: 9999;
}

.modal__main {
  padding: 30px;
  background-color: #fff;
  font-size: 16px;
  display: inline-block;
  position: relative;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}

.modal__header {
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24px;
  color: #047eef;
  margin-bottom: 27px;
  margin-top: 30px;
  position: relative;
}

.modal__header .logoimg {
  width: 140px;
}

.divider {
  margin: 0 10px;
}

.modal__header img {
  width: 75px;
}

.modal__header .modal__close {
  position: absolute;
  width: 28px;
  top: -40px;
  right: -10px;
  cursor: pointer;
}

/* Form styles */
.form-item {
  display: flex;
  flex-direction: row;
  margin-bottom: 24px;
  vertical-align: middle;
}

.form-item__required {
  color: #ff3d15;
}

.form-item__label-tips {
  font-size: 12px;
  color: #b2b2b2;
}

.form-item__label {
  text-align: right;
  padding-top: 10px;
  width: 72px;
}

.form-item__content {
  padding-left: 16px;
  max-width: 360px;
}

.employee-num__item {
  width: 152px;
  height: 50px;
  line-height: 50px;
  border: 1px solid #cecece;
  border-radius: 6px;
  text-align: center;
  margin-right: 20px;
  margin-bottom: 14px;
  display: inline-block;
  cursor: pointer;
}

.employee-num__item.active {
  color: #fff;
  background-color: #ff3d15;
  border: 1px solid #ff3d15;
}

.form-item__input {
  border: 1px solid #cecece;
  border-radius: 6px;
  outline: 0;
  width: 330px;
  height: 50px;
  line-height: 50px;
  padding: 14px;
}

.modal__btn {
  text-align: center;
  height: 50px;
  line-height: 50px;
  background: linear-gradient(90deg, #ff7214, #ff1616);
  border-radius: 6px;
  font-size: 20px;
  color: #fff;
  cursor: pointer;
  margin-top: -10px;
}

.modal__tips {
  color: #666;
  margin-top: 30px;
  width: 100%;
  text-align: center;
  font-size: 16px;
}
