<template>
  <header>
    <div class="common_header">
      <div class="common_header__title">
       <!--  <h1>{{ title }}</h1> -->
        <slot name="title"></slot>
      </div>
      <div class="common_header__content">
        <div class="content-detail">
          <div class="desc">
            <slot name="desc"></slot>
          </div>
          <div class="btn" @click="handleShowModal">
            {{ btnText }}
          </div>
        </div>
        <div class="content-img">
          <slot name="img"></slot>
        </div>
      </div>
    </div>
  </header>
</template>
<script setup>
const props = defineProps({
  title: {
    type: [String, Number],
    required: false,
  },
  desc: {},
  btnText: {
    type: [String, Number],
    required: false,
    default: '免费试用',
  },
});
const handleShowModal = openModal;
</script>
<style scoped lang="scss">
header {
  height: 223px;
  background: url('~/assets/roulei/roulei_header_bg_banner.png') no-repeat;
  background-size: 100% 100%;
}
.common_header {
  padding: 36px 21px 23px 24px;
}
.common_header__title :deep(h1) {
  font-size: 22px;
  font-family: PingFang SC-Semibold, PingFang SC;
  font-weight: 600;
  color: #ffffff;
  line-height: 26px;
}
.common_header__content {
  padding: 16px 0px 0px 0px;
  display: flex;
  justify-content: space-between;
}
.content-detail .desc :deep(p) {
  padding-bottom: 14px;
  font-size: 12px;
  font-family: PingFang SC-Regular, PingFang SC;
  font-weight: 400;
  color: #ffffff;
  line-height: 14px;
}
.content-detail .btn {
  display: inline-block;
  text-align: center;
  width: 98px;
  height: 27px;
  margin-top: 5px;
  line-height: 27px;
  font-size: 12px;
  font-family: PingFang SC-Regular, PingFang SC;
  font-weight: 400;
  color: white;
  background: #FF9607;
  padding: 5 25px;
  border-radius: 5px 5px 5px 5px;
  cursor: pointer;
  position: relative;
  z-index: 1;
}
.content-img {
  width: 120px;
  height: 115px;
  display: flex;
  justify-content: center;
  align-items: center;
  background: #eaeffa;
  border-radius: 5px 5px 5px 5px;
}
.content-img :deep(img) {
  width: 120px;
  height: 115px;
  background-size: 100% 100%;
}
</style>
